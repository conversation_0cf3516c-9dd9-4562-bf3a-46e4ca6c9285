import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface TaiKhoanDaiLyContextProps {
  danhSachTaiKhoanDaiLy: Array<CommonExecute.Execute.ITaiKhoanDaiLy>;
  loading: boolean;
  loadingVT: boolean;
  tongSoDong: number;
  tongSoDongVaiTro: number;
  tongSoDongNhom: number;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  defaultFormValue: object;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  menuNguoiSuDung: Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_menu"]>[0]>;
  chucNangNguoiSuDung: Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_quyen"]>[0]>;
  donViQuanLyNguoiSuDung: Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_qly"]>[0]>;
  danhSachVaiTroChucNang: Array<CommonExecute.Execute.IVaiTroChucNang>;
  danhSachNhomChucNang: Array<CommonExecute.Execute.INhomChucNang>;
  // layDanhSachChucNang: () => Promise<void>;
  chucNangSelected: Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_quyen"]>[0]>;
  donViSelected: Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_qly"]>[0]>;
  menuSelected: Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_menu"]>[0]>;
  listDaiLy: Array<CommonExecute.Execute.IDanhMucChucDanh>;
  getListDaiLy: (ma_doi_tac_ql: string) => void;
  setDonViQuanLySelected: React.Dispatch<React.SetStateAction<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_qly"]>[0]>>>;
  setMenuSelected: React.Dispatch<React.SetStateAction<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_menu"]>[0]>>>;
  setChucNangSelected: React.Dispatch<React.SetStateAction<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_quyen"]>[0]>>>;
  layChucNangTheoVaiTro: (params: ReactQuery.IDanhSachChucNangTheoVaiTroParams) => void;
  layChucNangTheoNhom: (params: ReactQuery.IDanhSachChucNangTheoNhomParams) => void;
  getListChiNhanhTheoDoiTac: () => void;
  onUpdateTaiKhoanDaiLy: (item: ReactQuery.IUpdateTaiKhoanDaiLyParams) => void;
  layDanhSachTaiKhoanDaiLyPhanTrang: (params: ReactQuery.ITimKiemPhanTrangTaiKhoanDaiLyParams) => void;
  getListDoiTac: () => Promise<void>;
  layChiTietTaiKhoanDaiLy: (params: ReactQuery.IChiTietTaiKhoanDaiLyParams) => Promise<CommonExecute.Execute.IChiTietNguoiSuDung | null>;
  layDanhSachVaiTroChucNangPhanTrang: (params: ReactQuery.ITimKiemPhanTrangVaiTroChucNangParams) => void;
  layDanhSachNhomChucNangPhanTrang: (params: ReactQuery.ITimKiemPhanTrangNhomChucNangParams) => void;
  setMenuNguoiSuDung: React.Dispatch<React.SetStateAction<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_menu"]>[0]>>>;
  setChucNangNguoiSuDung: React.Dispatch<React.SetStateAction<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_quyen"]>[0]>>>;
  setDonViQuanLyNguoiSuDung: React.Dispatch<React.SetStateAction<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_qly"]>[0]>>>;
}
