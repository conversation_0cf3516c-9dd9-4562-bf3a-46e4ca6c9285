import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import FormChiTietDanhMucSanPham, {IModalChiTietDanhMucSanPhamRef, Props, TRANG_THAI} from "./index.configs";
import {Col, Flex, Form, Modal, Row} from "antd";
import {useDanhMucSanPhamContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, ModalQuanLyFileCaNhan} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined, FolderOpenOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {PdfViewer} from "@src/components/PdfViewer";
import {WordViewer} from "@src/components/WordViewer";
import {env} from "@src/utils";
const {ma_doi_tac_ql, ma, ten, nv, stt, trang_thai} = FormChiTietDanhMucSanPham;

const ModalChiTietDanhMucSanPhamComponent = forwardRef<IModalChiTietDanhMucSanPhamRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucSanPham?: CommonExecute.Execute.IDanhMucSanPham) => {
      setIsOpen(true);
      form.resetFields();
      if (dataDanhMucSanPham) setChiTietDanhMucSanPham(dataDanhMucSanPham);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucSanPham, setChiTietDanhMucSanPham] = useState<CommonExecute.Execute.IDanhMucSanPham | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [selectedFileInfo, setSelectedFileInfo] = useState<{id: number; name: string; url: string; extension: string} | null>(null);
  const [isPdfViewerOpen, setIsPdfViewerOpen] = useState(false);
  const [isWordViewerOpen, setIsWordViewerOpen] = useState(false);
  const [disableXemFile, setDisableXemFile] = useState(false);
  const {loading, onUpdateDanhMucSanPham, filterParams, setFilterParams, danhSachSanPhamPhanTrang, listNghiepVu} = useDanhMucSanPhamContext();

  // ===== MODAL QUẢN LÝ FILE =====
  const modalQuanLyFileRef = useRef<any>(null);

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const closePdfViewer = () => {
    setIsPdfViewerOpen(false);
  };
  const closeWordViewer = () => {
    setIsWordViewerOpen(false);
  };
  useEffect(() => {
    if (chiTietDanhMucSanPham) {
      const arrFormData = [];
      for (const key in chiTietDanhMucSanPham) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDanhMucSanPham,
          value: chiTietDanhMucSanPham[key as keyof CommonExecute.Execute.IDanhMucSanPham],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucSanPham, form]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  useEffect(() => {
    if (chiTietDanhMucSanPham?.id_file !== null) {
      setDisableXemFile(false);
    } else {
      setDisableXemFile(true);
    }
  }, [chiTietDanhMucSanPham]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhMucSanPham(null);
    setSelectedFileInfo(null); // Reset selected file info
    form.resetFields();
    setFilterParams(filterParams);
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDanhMucSanPhamParams = form.getFieldsValue(); //lấy ra values của form
      const finalValues = {
        ...values,
        id_file: selectedFileInfo?.id,
        duong_dan: selectedFileInfo?.url,
        // loai_file: selectedFileInfo?.extension,
      };
      const response = await onUpdateDanhMucSanPham(finalValues);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        setIsOpen(false);
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  /**
   * Xử lý mở modal chọn file
   */
  const handleChonFile = useCallback(() => {
    if (modalQuanLyFileRef.current?.open) {
      modalQuanLyFileRef.current.open();
    }
  }, []);
  //xử lý xem file quy tắc
  const handleXemFile = useCallback(() => {
    console.log("mở file quy tắc");
    //gọi đến modal xem file
    if (chiTietDanhMucSanPham?.loai_file === ".pdf") {
      setIsPdfViewerOpen(true);
    }
    if (chiTietDanhMucSanPham?.loai_file === ".docx") {
      setIsWordViewerOpen(true);
    }
  }, [chiTietDanhMucSanPham]);
  /**
   * Xử lý khi chọn file từ modal
   */
  const handleChonFileSuccess = useCallback((filesSelected: any[]) => {
    console.log("handleChonFileSuccess", filesSelected);
    if (filesSelected && filesSelected.length > 0) {
      const selectedFile = filesSelected[0]; // Chỉ lấy file đầu tiên

      // Lấy id_file từ selectedFile - field 'id' trong File.GetFolder.IGetFolder
      const fileId = selectedFile.id;
      const fileName = selectedFile.ten_alias || selectedFile.ten;

      if (fileId) {
        // Lưu thông tin file đã chọn
        setSelectedFileInfo({
          id: Number(fileId),
          name: fileName,
          url: selectedFile.url_file,
          extension: selectedFile.extension,
        });
        console.log("File được chọn:", {
          id: fileId,
          name: fileName,
          url: selectedFile.url_file,
          extension: selectedFile.extension,
        });

        // Đóng modal sau khi set thành công
        if (modalQuanLyFileRef.current?.close) {
          modalQuanLyFileRef.current.close();
        }
      } else {
        console.warn("Không tìm thấy id file trong selectedFile:", selectedFile);
      }
    }
  }, []);

  //render footer
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="primary" onClick={handleXemFile} className="mr-2" disabled={disableXemFile}>
          Xem file quy tắc
        </Button>
        <Button type="primary" onClick={handleChonFile} className="mr-2" icon={<FolderOpenOutlined />}>
          {
            // tôi muốn có 3 lựa chọn 1 là selct.name hoặc chi tiết hoặc chọn file từ hệ thống
            // nếu có selectedFileInfo thì hiển thị tên file đã chọn, nếu không thì hiển thị tên file trong chi tiết, nếu không có chi tiết thì hiển thị "Chọn file từ hệ thống"

            selectedFileInfo ? `File: ${selectedFileInfo.name}` : chiTietDanhMucSanPham?.ten_file ? `File: ${chiTietDanhMucSanPham.ten_file}` : "Chọn file quy tắc"
          }
        </Button>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, disabled: chiTietDanhMucSanPham ? true : false})}
        {renderFormInputColum({...nv, options: listNghiepVu, disabled: chiTietDanhMucSanPham ? true : false})}
        {renderFormInputColum({...ma, disabled: chiTietDanhMucSanPham ? true : false})}
      </Row>
      <Row gutter={16}>
        {renderFormInputColum({...ten})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
        {renderFormInputColum({...stt})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhMucSanPham ? `Chi tiết Sản phẩm ${chiTietDanhMucSanPham.ten}` : "Tạo mới sản phẩm"}
            trang_thai_ten={chiTietDanhMucSanPham?.trang_thai_ten}
            trang_thai={chiTietDanhMucSanPham?.trang_thai}
          />
        }
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "55%",
          sm: "55%",
          md: "55%",
          lg: "55%",
          xl: "55%",
          xxl: "55%",
        }}
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        {renderForm()}
        {/* Modal quản lý file cá nhân */}
        <ModalQuanLyFileCaNhan ref={modalQuanLyFileRef} onClickChonFile={handleChonFileSuccess} />
        {/* PDF Viewer Component */}
        <PdfViewer
          fileUrl={chiTietDanhMucSanPham?.duong_dan ? env.VITE_BASE_URL + chiTietDanhMucSanPham?.duong_dan : ""}
          fileName={chiTietDanhMucSanPham?.ten_file || ""}
          open={isPdfViewerOpen}
          onClose={closePdfViewer}
        />

        {/* Word Viewer Component */}
        <WordViewer
          fileUrl={chiTietDanhMucSanPham?.duong_dan ? env.VITE_BASE_URL + chiTietDanhMucSanPham?.duong_dan : ""}
          fileName={chiTietDanhMucSanPham?.ten_file || ""}
          open={isWordViewerOpen}
          onClose={closeWordViewer}
        />
      </Modal>
    </Flex>
  );
});
ModalChiTietDanhMucSanPhamComponent.displayName = "ModalChiTietDanhMucSanPhamComponent";
export const ModalChiTietDanhMucSanPham = memo(ModalChiTietDanhMucSanPhamComponent, isEqual);
