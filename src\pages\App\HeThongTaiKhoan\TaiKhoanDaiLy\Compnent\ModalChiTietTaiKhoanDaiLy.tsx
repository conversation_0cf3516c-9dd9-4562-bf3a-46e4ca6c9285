import {CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {usePhongBan} from "@src/hooks";

import {Col, Form, Modal, Row, Tabs} from "antd";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useTaiKhoanDaiLyContext} from "../index.context";
import {FormChiTietTaiKhoanDaiLy, GIOI_TINH, initFormFields, TRANG_THAI_NGUOI_DUNG} from "./index.configs";
// import TabPhanQuyenChucNangTheoVaiTro from "./TabPhanQuyenChucNangTheoVaiTro";
// import TabPhanQuyenChucNangTheoNhom from "./TabPhanQuyenChucNangTheoNhom";
// import TabDonViQuanLy from "./TabDonViQuanLy";
import dayjs from "dayjs";
import {hashPasswordWithAccount} from "@src/utils/password";

interface Props {
  danhSachTaiKhoanDaiLy: Array<CommonExecute.Execute.ITaiKhoanDaiLy>;
}

export interface IModalChiTietTaiKhoanDaiLyRef {
  open: (data?: CommonExecute.Execute.ITaiKhoanDaiLy) => void;
  close: () => void;
}

const ModalChiTietTaiKhoanDaiLy = forwardRef<IModalChiTietTaiKhoanDaiLyRef, Props>(({danhSachTaiKhoanDaiLy}: Props, ref) => {
  const listPhongBan = usePhongBan();

  const {
    onUpdateTaiKhoanDaiLy,
    listDoiTac,
    getListChiNhanhTheoDoiTac,
    listChiNhanh,
    chucNangSelected,
    // ************************,
    layChiTietTaiKhoanDaiLy,
    // setDonViQuanLyTaiKhoanDaiLy,
    // setMenuTaiKhoanDaiLy,
    listDaiLy,
    getListDaiLy,
  } = useTaiKhoanDaiLyContext();

  const {dthoai, ten, email, mat_khau, ma_dly, ma, ma_doi_tac, ngay_hl, ngay_kt, trang_thai, ngay_sinh, gioi_tinh, cmt} = FormChiTietTaiKhoanDaiLy;
  const [************************] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const formValues = Form.useWatch([], ************************);
  const [chiTietTaiKhoanDaiLy, setChiTietTaiKhoanDaiLy] = useState<CommonExecute.Execute.ITaiKhoanDaiLy | null>(null);
  const [filteredPhongBan, setFilteredPhongBan] = useState<Array<CommonExecute.Execute.IDanhMucPhongBan>>([]);
  const [activeTab, setActiveTab] = useState("1");
  const [filteredChiNhanh, setFilteredChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  // const [selectedDonViQuanLy, setSelectedDonViQuanLy] = useState<TableDonViQuanLyDataType[]>([]);
  // const [selectedMenu, setSelectedMenu] = useState<TableMenuNguoiDungDataType[]>([]);
  const [disableCoppy, setDisableCoppy] = useState<boolean>(false);
  // Hàm callback nhận dữ liệu từ TabDonViQuanLy
  const closeModal = useCallback(() => {
    setIsOpen(false);

    // setchitietMenu(null);
    // formThemMenuCha.resetFields();
    // setFilterParams({...filterParams});
    // console.log("filterparamss", filterParams);
  }, []);
  useImperativeHandle(ref, () => ({
    open: (dataChiTietTaiKhoanDaiLy?: CommonExecute.Execute.ITaiKhoanDaiLy) => {
      setIsOpen(true);
      setIsEdit(false);
      setDisableSubmit(true);

      if (!dataChiTietTaiKhoanDaiLy) {
        // setMenuTaiKhoanDaiLy([]);
        // ************************([]);
        // setDonViQuanLyTaiKhoanDaiLy([]);
        setDisableCoppy(true);
      }
      if (dataChiTietTaiKhoanDaiLy) {
        getListDaiLy(dataChiTietTaiKhoanDaiLy.ma_doi_tac);
        setIsEdit(true);
        setChiTietTaiKhoanDaiLy(dataChiTietTaiKhoanDaiLy);
        setDisableCoppy(false);
      }
    },
    close: () => setIsOpen(false),
  }));
  // init form data gọi vào index.configs
  useEffect(() => {
    // console.log("chi tiết người sửa dung", chiTietTaiKhoanDaiLy);

    initFormFields(************************, chiTietTaiKhoanDaiLy);
  }, [chiTietTaiKhoanDaiLy]);

  useEffect(() => {
    if (isOpen) {
      setActiveTab("1");
    }
  }, [isOpen]);
  //xử lý validate form
  useEffect(() => {
    ************************
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [************************, formValues]);

  //Bấm Update
  const onPressUpdateTaiKhoanDaiLy = async () => {
    try {
      const values: ReactQuery.IUpdateTaiKhoanDaiLyParams = ************************.getFieldsValue(); //lấy ra values của form
      //mã hoá password
      console.log("values", values);
      console.log("ormChiTietTaiKhoanDaiLy.getFieldValue(mat_khau)", ************************.getFieldValue(mat_khau));
      if (values.mat_khau) {
        const passSHA256: string = hashPasswordWithAccount(values.ma, values.mat_khau);
        values.mat_khau = passSHA256;
      }
      const param: ReactQuery.IUpdateTaiKhoanDaiLyParams = {
        ...values,
        mat_khau: values.mat_khau || "",
        ngay_hl: dayjs(values.ngay_hl).format("YYYYMMDD"),
        ngay_kt: dayjs(values.ngay_hl).format("YYYYMMDD"),
        ngay_sinh: dayjs(values.ngay_sinh).format("YYYYMMDD"),
        // qly: donViSelected.map(row => ({
        //   ma_doi_tac_ql: row.ma_doi_tac_ql,
        //   ma_chi_nhanh_ql: row.ma_chi_nhanh_ql,
        // })),
        // menu: menuSelected.map(row => ({
        //   ma: row.ma,
        // })),
        // quyen: chucNangSelected.map(row => ({
        //   ma: row.ma_chuc_nang,
        // })),
      };
      console.log("params", param);
      const response = await onUpdateTaiKhoanDaiLy(param);
      await layChiTietTaiKhoanDaiLy({ma_doi_tac: values.ma_doi_tac, ma: values.ma});
      // if (response === -1) {
      //   console.log("cập nhật thành công");
      //   closeModal();
      // } else {
      //   console.log("cập nhật thất bại");
      // }
      // const dataChiTiet = await layChiTietTaiKhoanDaiLy(values);
      // setChiTietTaiKhoanDaiLy(dataChiTiet);
    } catch (error: any) {
      console.log("onConfirm", error);
    }
  };

  const handleChangeDoiTac = (maDoiTac: string) => {
    console.log("hàm chang đối tác", maDoiTac);
    getListDaiLy(maDoiTac);
  };

  // Xử lý khi chọn chi nhánh
  const handleChangeChiNhanh = (maChiNhanh: string) => {
    // Lọc danh sách phòng ban theo mã chi nhánh
    const filtered = listPhongBan.listPhongBan.filter(pb => pb.ma_chi_nhanh === maChiNhanh);
    setFilteredPhongBan(filtered);
    // Lấy giá trị phòng ban hiện tại từ chiTietTaiKhoanDaiLy
    const currentPhong = chiTietTaiKhoanDaiLy?.phong;
    // Kiểm tra phòng ban hiện tại có trong filtered không
    const found = filtered.find(pb => pb.value === currentPhong);
    if (found) {
      ************************.setFieldsValue({phong: currentPhong}); // Nếu có thì set lại giá trị
    } else {
      ************************.setFieldsValue({phong: null}); // Nếu không có thì reset
    }
  };
  const onCoppyTaiKhoan = () => {
    if (chiTietTaiKhoanDaiLy) {
      ************************.setFieldsValue({
        ...chiTietTaiKhoanDaiLy,
        ma: "",
        mat_khau: "",
        ngay_hl: dayjs(),
        ngay_kt: dayjs(),
        ngay_sinh: dayjs(),
        gioi_tinh: "NAM",
        cmt: "",
        ma_dly: undefined,

        trang_thai: "D",
        ma_doi_tac: undefined,
        ma_chi_nhanh: undefined,
        phong: undefined,
        ma_chuc_danh: undefined,
        ten: "",
        email: "",
        dthoai: "",
      });
      setIsEdit(false);
    }
  };
  const handleTabChange = async (activeKey: string) => {
    setActiveTab(activeKey);
  };
  //renderFooter Modal
  const renderFooter = () => {
    return (
      <div className="modal-footer-spaced">
        {/* nút coppy */}
        {/* <Button type="default" onClick={onCoppyTaiKhoan} disabled={disableCoppy} className="mr-2" icon={<CopyOutlined />}>
          Coppy
        </Button> */}
        <Button type="primary" htmlType="submit" form="formUpdatePhongBan" disabled={disableSubmit} onClick={onPressUpdateTaiKhoanDaiLy} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </div>
    );
  };
  //Render

  const renderFormInputColum = (props?: any, span = 6) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderTabs = () => {
    const isEdit = !!chiTietTaiKhoanDaiLy;

    return (
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <Tabs.TabPane key="1" tab="Đơn vị quản lý / Menu">
          <TabDonViQuanLy
            // filterValues={filterValues}
            chiTietTaiKhoanDaiLy={chiTietTaiKhoanDaiLy}
            // onDataChange={handleDonViQuanLyChange} // Truyền callback cho đơn vị quản lý
            // onDataChangeMenu={handleMenuChange}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="2" tab="Phân quyền chức năng theo vai trò">
          <TabPhanQuyenChucNangTheoVaiTro
            // filterValues={filterValues}
            chiTietTaiKhoanDaiLy={chiTietTaiKhoanDaiLy}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="3" tab="Phân quyền chức năng theo nhóm">
          <TabPhanQuyenChucNangTheoNhom
            // filterValues={filterValues}
            chiTietTaiKhoanDaiLy={chiTietTaiKhoanDaiLy}
          />
        </Tabs.TabPane>
      </Tabs>
    );
  };
  return (
    <Modal
      className="modal-chi-tiet-tai-khoan"
      title={
        <HeaderModal
          title={chiTietTaiKhoanDaiLy ? `Thông tin người dùng ${chiTietTaiKhoanDaiLy.ten}` : "Thêm người sử dụng"}
          trang_thai_ten={chiTietTaiKhoanDaiLy?.trang_thai_ten}
          trang_thai={chiTietTaiKhoanDaiLy?.trang_thai}
        />
      }
      centered
      open={isOpen}
      onOk={() => setIsOpen(false)}
      onCancel={() => {
        ************************.resetFields();
        setIsOpen(false);
        setChiTietTaiKhoanDaiLy(null);
      }}
      footer={renderFooter}
      closable
      maskClosable={false}
      width="70vw"
      style={{
        top: 0,
        left: 0,
        padding: 0,
      }}
      styles={{
        body: {
          // height: "76vh",
        },
      }}
      // className="custom-full-modal m-2"
    >
      <Form id="formUpdateTaiKhoanDaiLy" onFinish={onPressUpdateTaiKhoanDaiLy} form={************************} layout="vertical" autoComplete="on">
        <Row gutter={16}>
          {renderFormInputColum({...ma_doi_tac, options: listDoiTac, onChange: (value: string) => handleChangeDoiTac(value), disabled: isEdit ? true : false})}
          {renderFormInputColum({...ma, disabled: isEdit ? true : false})}
          {renderFormInputColum(ten)}
          {renderFormInputColum({...mat_khau})}
        </Row>
        <Row gutter={16}>
          {renderFormInputColum({...ma_dly, options: listDaiLy, onChange: () => {}})}
          {renderFormInputColum({...ngay_hl}, 5)}
          {renderFormInputColum({...ngay_kt}, 5)}
          {renderFormInputColum({...dthoai}, 5)}
          {renderFormInputColum({...gioi_tinh, options: GIOI_TINH}, 3)}
        </Row>
        <Row gutter={16}>
          {renderFormInputColum(email)}
          {renderFormInputColum(ngay_sinh)}
          {renderFormInputColum(cmt)}
          {renderFormInputColum({...trang_thai, options: TRANG_THAI_NGUOI_DUNG})}
        </Row>
      </Form>
      {/* {renderTabs()} */}
    </Modal>
  );
});
ModalChiTietTaiKhoanDaiLy.displayName = "ModalChiTietTaiKhoanDaiLy";
export default ModalChiTietTaiKhoanDaiLy;
// ModalChiTietTaiKhoanDaiLyComponent.displayName = "ModalChiTietTaiKhoanDaiLyComponent";
// export const ModalChiTietTaiKhoanDaiLy = memo(ModalChiTietTaiKhoanDaiLyComponent, isEqual);
