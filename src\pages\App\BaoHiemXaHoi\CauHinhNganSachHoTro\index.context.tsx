/**
 * - Tạo React Context để chia sẻ state và methods giữa các component
 * - Định nghĩa interface cho context props
 * - <PERSON><PERSON> cấp custom hook để các component con dễ dàng access context
 */
import {createContext, useContext} from "react";

import {ICauHinhNganSachHoTroContextProps} from "./index.model";

// Tạo React Context để chia sẻ state và methods giữa các component
//khởi tạo giá trị mặc định
export const CauHinhNganSachHoTroContext = createContext<ICauHinhNganSachHoTroContextProps>({
  listTinhThanh: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  danhSachNganSachHoTroNgayApDung: [],
  chiTietNganSachHoTro: {} as CommonExecute.Execute.INganSachHoTro,
  listDonViThuHo: [],
  ngayAdMoiTao: null,
  listLoaiHoGiaDinh: [],

  getListLoaiHoGiaDinh: async () => Promise.resolve(),
  setDanhSachNganSachHoTroNgayApDung: () => {},
  setChiTietNganSachHoTro: () => {},
  setNgayAdMoiTao: () => {},
  CapNhatNganSachHoTro: async () => Promise.resolve(false),
  layChiTietNganSachHoTro: async () => Promise.resolve({} as CommonExecute.Execute.INganSachHoTro),
  chiTietTinhThanh: {} as CommonExecute.Execute.IDanhMucTinhThanh,
  layDanhSachNganSachHoTroNgayApDung: async () => Promise.resolve({} as CommonExecute.Execute.INganSachHoTroNgayApDung),
  getListTinhThanh: async () => Promise.resolve(),
  layChiTietDanhMucTinhThanh: async () => Promise.resolve({} as CommonExecute.Execute.IDanhMucTinhThanh),
  setFilterParams: () => {},
  updateNganSachHoTroNgayApDung: async () => Promise.resolve(false),
  xoaNgayApDungNganSachHoTro: async () => Promise.resolve(false),
});

// Custom hook để các component con có thể dễ dàn`g access context
export const useCauHinhNganSachHoTroContext = () => useContext(CauHinhNganSachHoTroContext);
