import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface TableTaiKhoanDaiLyDataType {
  key: string;
  ten?: string;
  ma_doi_tac?: string;
  doi_tac_ten_tat?: string;
  ma_dly?: string;
  ten_dly?: string;
  ma?: string;
  stt?: number;
  sott?: number;
  trang_thai?: string;
  trang_thai_ten?: string;
  dthoai?: string;
  email?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  ngay_hl?: string;
  ngay_kt?: string;
  ngay_sinh?: string;
  gioi_tinh?: string;
  cmt?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const taiK<PERSON>anDaiLyColumns: TableProps<TableTaiKhoanDaiLyDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: colWidthByKey.sott,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên người dùng",
    dataIndex: "ten",
    key: "ten",
    width: 200,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã tài khoản",
    dataIndex: "ma",
    key: "ma",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Đại lý",
    dataIndex: "ten_dly",
    key: "ten_dly",
    width: 180,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Email",
    dataIndex: "email",
    key: "email",
    width: 200,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Điện thoại",
    dataIndex: "dthoai",
    key: "dthoai",
    width: 150,
    ...defaultTableColumnsProps,
    align: "center",
  },
  {
    title: "CMT/CCCD",
    dataIndex: "cmt",
    key: "cmt",
    width: 150,
    ...defaultTableColumnsProps,
    align: "center",
  },

  {title: "Ngày sinh", dataIndex: "ngay_sinh", key: "ngay_sinh", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Giới tính", dataIndex: "gioi_tinh", key: "gioi_tinh", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, ...defaultTableColumnsProps},
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

export const setFormFields = (form: any, chiTietPhongBan: any) => {
  if (chiTietPhongBan) {
    form.setFields([
      {
        name: "ten",
        value: chiTietPhongBan.ten || "",
      },
      {
        name: "ma",
        value: chiTietPhongBan.ma || "",
      },
      {
        name: "stt",
        value: chiTietPhongBan.stt || "",
      },
      {
        name: "dthoai",
        value: chiTietPhongBan.dthoai || "",
      },
      {
        name: "trang_thai",
        value: chiTietPhongBan.trang_thai,
      },
      {
        name: "ma_doi_tac",
        value: chiTietPhongBan.ma_doi_tac,
      },
    ]);
  }
};

//option select trạng thái
export const optionTrangThaiTaiKhoanDaiLySelect = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngừng sử dụng"},
];

//Form
export interface IFormTimKiemPhanTrangTaiKhoanDaiLyFieldsConfig {
  ma_doi_tac: IFormInput;
  ma_dly: IFormInput;
  ma: IFormInput;
  nd_tim: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemPhanTrangTaiKhoanDaiLy: IFormTimKiemPhanTrangTaiKhoanDaiLyFieldsConfig = {
  ma_doi_tac: {
    component: "select",
    name: "ma_doi_tac",
    label: "Đối tác",
    placeholder: "Chọn mã đối tác",
  },
  ma_dly: {
    component: "select",
    name: "ma_dly",
    label: "Đại lý",
    placeholder: "Đại lý",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã tài khoản",
    placeholder: "Nhập mã tài khoản",
  },
  nd_tim: {
    component: "input",
    name: "nd_tim",
    label: "Tìm kiếm thông tin",
    placeholder: "Nhập tên người dùng/tài khoản",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
};

export const radioItemTrangThaiTaiKhoanNSDTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
