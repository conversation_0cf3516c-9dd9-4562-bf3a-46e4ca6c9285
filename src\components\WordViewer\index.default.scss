.modal-word-viewer .ant-modal-body {
  .ant-tabs-top .ant-tabs-content-holder .ant-tabs-content-top .ant-tabs-tabpane.ant-tabs-tabpane-active {
    height: 100%;
  }
  .ant-tabs-top .ant-tabs-content-holder .ant-tabs-content-top {
    height: 100%;
  }
  .ant-modal-wrap {
    overflow: hidden;
  }
}

// CSS cho docx-preview để cải thiện hiển thị
.docx-viewer {
  .docx-wrapper {
    // max-width: 100% !important;
    // overflow-x: auto;

    // Cải thiện hiển thị table
    table {
      max-width: 100% !important;
      table-layout: auto !important;
      word-wrap: break-word;
      word-break: break-word;

      td,
      th {
        max-width: 200px;
        word-wrap: break-word;
        word-break: break-word;
        white-space: normal !important;
        padding: 4px 8px;
      }
    }

    // C<PERSON>i thiện hiển thị paragraph
    p {
      word-wrap: break-word;
      word-break: break-word;
      line-height: 1.6;
      margin-bottom: 8px;
    }

    // <PERSON><PERSON>i thiện hiển thị div
    div {
      max-width: 100%;
      word-wrap: break-word;
    }

    // Responsive cho mobile
    @media (max-width: 768px) {
      font-size: 14px;

      table {
        font-size: 12px;

        td,
        th {
          max-width: 120px;
          padding: 2px 4px;
        }
      }
    }
  }
}
