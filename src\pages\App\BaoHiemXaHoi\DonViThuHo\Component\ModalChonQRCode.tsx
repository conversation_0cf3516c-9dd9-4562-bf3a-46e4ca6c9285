import {CheckOutlined} from "@ant-design/icons";
import {<PERSON><PERSON>, ModalQuanLyFileCaNhan} from "@src/components";
import {Flex, Modal} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useQuanLyDonViThuHoContext} from "../index.context";
import {QRCodeProps, IModalChonQRCodeRef} from "./Constant";
import "../index.default.scss";
import {env} from "@src/utils";

const ModalChonQRCodeComponent = forwardRef<IModalChonQRCodeRef, QRCodeProps>(({chiTietDonViThuHo, onClickChonFileSuccess}: QRCodeProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDonViThuHo?: CommonExecute.Execute.IDonViThuHo) => {
      setIsOpen(true);
      //   if (dataDonViThuHo); // nếu có dữ liệu -> set chi tiết DonViThuHo -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  //   const [chiTietDonViThuHo, setChiTietDonViThuHo] = useState<CommonExecute.Execute.IDonViThuHo | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDonViThuHo, getListDonViThuHo, loading, listNganHang} = useQuanLyDonViThuHoContext();
  //   const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //   const formValues = Form.useWatch([], form);
  const modalQuanLyFileRef = useRef<any>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [fileSelected, setFileSelected] = useState<any>(null);
  // init form data - load dữ liệu vào form khi sửa
  //   useEffect(() => {
  //     if (chiTietDonViThuHo) {
  //       const arrFormData = [];
  //       for (const key in chiTietDonViThuHo) {
  //         arrFormData.push({
  //           name: key,
  //           value: chiTietDonViThuHo[key as keyof CommonExecute.Execute.IDonViThuHo],
  //         });
  //       }
  //       form.setFields(arrFormData);
  //     }
  //   }, [chiTietDonViThuHo]);

  //   //xử lý validate form
  //   useEffect(() => {
  //     form
  //       .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
  //       .then(() => {
  //         setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
  //       })
  //       .catch(() => {
  //         setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
  //       });
  //   }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setFileSelected(null);
    onClickChonFileSuccess?.([]);
    // setChiTietDonViThuHo(null);
    // form.resetFields();
  }, []);
  //   console.log("chiTietDonViThuHo.file_qrcode", chiTietDonViThuHo.file_qrcode);
  useEffect(() => {
    if (chiTietDonViThuHo?.id_file_qrcode) {
      console.log("chiTietDonViThuHo.file_qrcode", chiTietDonViThuHo.file_qrcode);
      setImageUrl(env.VITE_BASE_URL + chiTietDonViThuHo.file_qrcode);
    } else {
      setImageUrl(null);
    }
  }, [chiTietDonViThuHo]);

  //Bấm Update
  //   const onConfirm = async () => {
  //     //đẩu dữ liệu sang modal cha
  //     try {
  //       const values: ReactQuery.ICapNhatDonViThuHoParams = form.getFieldsValue(); //lấy ra values của form
  //       console.log("values", values);
  //       closeModal();
  //     } catch (error) {
  //       console.log("onConfirm", error);
  //     }
  //   };

  //Bấm Chọn - truyền fileSelected lên modal cha
  const handleChonFileClick = useCallback(() => {
    try {
      if (fileSelected && onClickChonFileSuccess) {
        // Truyền fileSelected dưới dạng array để phù hợp với interface
        onClickChonFileSuccess([fileSelected]);
        closeModal();
        console.log("Đã truyền fileSelected lên modal cha:", fileSelected);
      } else {
        console.warn("Chưa chọn file hoặc không có callback function");
      }
    } catch (error) {
      console.log("handleChonFileClick error", error);
    }
  }, [fileSelected, onClickChonFileSuccess]);
  const handleChonFile = useCallback(() => {
    if (modalQuanLyFileRef.current?.open) {
      modalQuanLyFileRef.current.open();
    }
  }, []);
  const handleChonFileSuccess = useCallback((filesSelected: any[]) => {
    console.log("handleChonFileSuccess", filesSelected);
    if (filesSelected && filesSelected.length > 0) {
      const selectedFile = filesSelected[0];
      const fileId = selectedFile.id;
      // const fileName = selectedFile.ten_alias || selectedFile.ten || "File không tên";
      // form.setFieldValue("file_qrcode", selectedFile.url_file);
      console.log("selectedFile", selectedFile);
      setFileSelected(selectedFile);
      // console.log("filevalue", form.getFieldsValue());
      if (fileId) {
        // form.setFieldValue("id_file", Number(fileId));
        //hiển thị hình ảnh
        if (selectedFile.url_file && selectedFile.extension) {
          const imageExtensions = [".jpg", ".jpeg", ".png", ".gif"];
          if (imageExtensions.includes(selectedFile.extension.toLowerCase())) {
            const imageUrl = env.VITE_BASE_URL + selectedFile.url_file;
            setImageUrl(imageUrl);
          }
        }

        if (modalQuanLyFileRef.current?.close) {
          modalQuanLyFileRef.current.close();
        }
      }
    }
  }, []);
  // RENDER
  const renderChonFile = () => {
    return (
      <div
        onClick={handleChonFile}
        style={{
          width: 150,
          height: 150,
          border: "2px dashed #d9d9d9",
          borderRadius: 8,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          overflow: "hidden",
          background: "#fafafa",
        }}>
        {imageUrl ? <img src={imageUrl} alt="selected" style={{width: "100%", height: "100%", objectFit: "cover"}} /> : <span style={{color: "#999"}}>+</span>}
      </div>
    );
  };
  //FOOTER
  const renderFooter = () => {
    return (
      <Button type="primary" onClick={handleChonFileClick} className="mr-2" icon={<CheckOutlined />} iconPosition="end">
        Chọn
      </Button>
    );
  };

  //Render
  return (
    <Flex vertical gap="" align="center">
      <Modal
        // title="Chọn QR code"
        // centered
        className="modal-chi-tiet-don-vi-thu-ho"
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={200}
        // style={{
        //   top: 30,
        // }}
        styles={{
          body: {
            paddingTop: "16px",
            // paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderChonFile()}
        <ModalQuanLyFileCaNhan ref={modalQuanLyFileRef} onClickChonFile={handleChonFileSuccess} />
      </Modal>
    </Flex>
  );
});

ModalChonQRCodeComponent.displayName = "ModalChonQRCodeComponent";
export const ModalChonQRCode = memo(ModalChonQRCodeComponent, isEqual);
