// SCREEN HỢP ĐỒNG CON NGƯỜI
#HOP_DONG_CON_NGUOI {
  .antd-table-hide-scroll .ant-table-body {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }

  .antd-table-hide-scroll .ant-table-body::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }

  .ant-table-container {
    // min-height: 620px;
    scrollbar-width: thin;
    scrollbar-color: #eaeaea transparent;
    scrollbar-gutter: stable;
  }
}

//CSS MODAL, DO MODAL ĐƯỢC HIẺN THỊ Ở MỘT DIV KHÁC, NÊN KHI
//MODAL TÌM KHÁCH HÀNG
.modal-tim-khach-hang-hop-dong-con-nguoi {
  .custom-row-selected {
    background-color: #9abcea80 !important;
  }
  .custom-row-selected.ant-table-row:hover {
    background-color: #9abcea80 !important;
  }
}

//MODAL TÌM CÁN BỘ
.modal-tim-can-bo-hop-dong-con-nguoi {
  .custom-row-selected {
    background-color: #9abcea80 !important;
  }
  .custom-row-selected.ant-table-row:hover {
    background-color: #9abcea80 !important;
  }
}

//MODAL TÌM ĐẠI LÝ
.modal-tim-dai-ly-khai-thac-hop-dong-con-nguoi {
  .custom-row-selected {
    background-color: #9abcea80 !important;
  }
  .custom-row-selected.ant-table-row:hover {
    background-color: #9abcea80 !important;
  }
}

//MODAL THÊM HỢP ĐỒNG
.modal-them-hop-dong-con-nguoi {
  .custom-row-selected {
    background-color: #9abcea80 !important; //style row khách hàng khi được chọn -> highlight background
  }
  .custom-row-selected.ant-table-row:hover {
    background-color: #9abcea80 !important;
  }
  .table-row-active {
    background-color: #96bf49 !important;
  }
  .table-danh-sach-nguoi-duoc-bao-hiem-hop-dong-con-nguoi .ant-table-title {
    padding: 8px 8px !important;
  }
  .no-header-border-radius .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }
  .table-thong-tin-ky-thanh-toan {
    .ant-table-title {
      padding: 0 !important;
    }
  }
  .table-lich-su-boi-thuong {
    td.ant-table-cell.ant-table-cell-ellipsis {
      overflow: unset;
    }
  }
  .table-thong-tin-dong-bao-hiem {
    .ant-table-title {
      padding: 0 !important;
    }
  }

  .table-thong-tin-tai-bao-hiem {
    .ant-table-title {
      padding: 0 !important;
    }
    .ant-table .ant-table-header {
      border-radius: 0 !important;
      border-top: 1px solid #f0f0f0 !important;
    }
  }

  // TABLE BẢNG QUYỀN LỢI
  .table-quyen-loi {
    // .ant-table-container {
    // min-height: 620px;
    // scrollbar-width: thin;
    // scrollbar-color: #eaeaea transparent;
    // scrollbar-gutter: stable;
    // }
    .ant-table-container {
      max-height: calc(80vh - 44px - 26px - 54px - 22px); //78 : modal height, 44 : footer modal height, 26 : header modal height,
      overflow-y: auto;
      // overflow: auto;
    }
    .ant-table-row:hover td {
      background-color: #e8f5e9 !important; //test
    }
    .ant-form-item .ant-form-item-control-input {
      min-height: 22px;
    }
    .ant-table-wrapper .ant-table .ant-table-header {
      border-radius: 0 !important;
    }
    .ant-table-title {
      padding: 0 !important;
    }
  }

  .table-nguoi-phu-thuoc {
    .ant-table-title {
      padding: 0 !important;
    }
  }

  .ant-form-item .ant-form-item-label {
    padding-bottom: 0px !important;

    label {
      font-size: 12px !important;
      font-weight: 600 !important;
    }
  }
}

//MODAL THÊM QUYỀN LỢI
.modal-them-quyen-loi-hop-dong-con-nguoi {
  .tree-quyen-loi {
    max-height: calc(60vh - 32px); // 32px : search input,
    overflow-y: auto;
  }
}

.modal-them-quyen-loi {
  .tree-quyen-loi {
    max-height: calc(60vh - 32px); // 32px : search input,
    overflow-y: auto;
  }
}

.modal-ky-thanh-toan {
  .no-hover-table .ant-table-tbody > tr.ant-table-row:hover > td {
    background: none !important;
  }
  .no-header-border-radius .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }
}

.modal-doi-tuong-ap-dung-dong-bh {
  .no-header-border-radius .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }

  .table-doi-tuong-ap-dung-dong-bh {
    .ant-table-title {
      padding: 0 !important;
    }
  }
  .hide-scrollbar .ant-table-body {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    border-bottom: 1px solid #f0f0f0;
  }

  .hide-scrollbar .ant-table-body::-webkit-scrollbar {
    display: none;
    /* Chrome/Safari/Webkit */
  }
  .ant-form-item .ant-form-item-control-input {
    min-height: 22px;
  }
}
.modal-doi-tuong-ap-dung-tai-bh {
  .no-header-border-radius .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }

  .table-doi-tuong-ap-dung-tai-bh {
    .ant-table-title {
      padding: 0 !important;
    }
  }
  .hide-scrollbar .ant-table-body {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    border-bottom: 1px solid #f0f0f0;
  }

  .hide-scrollbar .ant-table-body::-webkit-scrollbar {
    display: none;
    /* Chrome/Safari/Webkit */
  }
  .ant-form-item .ant-form-item-control-input {
    min-height: 22px;
  }
}
