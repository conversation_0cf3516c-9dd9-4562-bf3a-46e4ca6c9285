import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {TaiKhoanDaiLyContext} from "./index.context";
import {TaiKhoanDaiLyContextProps} from "./index.model";
import {message} from "antd";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/
const TaiKhoanDaiLyProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachTaiKhoanDaiLy, ************************] = useState<Array<CommonExecute.Execute.ITaiKhoanDaiLy>>([]);
  const [chiTietTaiKhoanDaiLy, setChiTietTaiKhoanDaiLy] = useState<CommonExecute.Execute.IChiTietNguoiSuDung>({});
  const [menuNguoiSuDung, setMenuNguoiSuDung] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_menu"]>[0]>>([]);
  const [chucNangNguoiSuDung, setChucNangNguoiSuDung] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_quyen"]>[0]>>([]);
  const [donViQuanLyNguoiSuDung, setDonViQuanLyNguoiSuDung] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_qly"]>[0]>>([]);
  const [tongSoDongVaiTro, setTongSoDongVaiTro] = useState<number>(0);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [tongSoDongNhom, setTongSoDongNhom] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [listDaiLy, setListDaiLy] = useState<Array<CommonExecute.Execute.IDanhMucDaiLy>>([]);
  const [listChiNhanh, setListChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [danhSachVaiTroChucNang, setDanhSachVaiTroChucNang] = useState<Array<CommonExecute.Execute.IVaiTroChucNang>>([]);
  const [danhSachNhomChucNang, setDanhSachNhomChucNang] = useState<Array<CommonExecute.Execute.INhomChucNang>>([]);
  const [donViSelected, setDonViQuanLySelected] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_qly"]>[0]>>([]);
  const [menuSelected, setMenuSelected] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_menu"]>[0]>>([]);
  const defaultFormValue: ReactQuery.ITimKiemPhanTrangTaiKhoanDaiLyParams = {
    ma_doi_tac: "",
    ma_dly: "",
    trang_thai: "",
    nd_tim: "",
    trang: 1,
    so_dong: 10,
  };
  const [chucNangSelected, setChucNangSelected] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_quyen"]>[0]>>([]);
  useEffect(() => {
    // layDanhSachPhongBan()
    initData();
  }, []);

  const initData = () => {
    layDanhSachTaiKhoanDaiLyPhanTrang(defaultFormValue);
    getListDoiTac();
    getListChiNhanhTheoDoiTac();
  };
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  /* ĐỐI TÁC */
  const getListDaiLy = useCallback(
    async (ma_doi_tac_ql: string) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ma_doi_tac_ql,
          actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DAI_LY,
        });
        setListDaiLy(response?.data?.map(item => ({...item, ten: item.ten})));
        console.log("getListDaiLy", response?.data);
      } catch (error) {
        console.log("getListChucDanh   error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  //DS phòng ban phân trang - Danh sách người dùng
  const layDanhSachTaiKhoanDaiLyPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangTaiKhoanDaiLyParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_TAI_KHOAN_DAI_LY,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        ************************(data);
        setTongSoDong(response.data.tong_so_dong);
        console.log("tổng số dòng", tongSoDong);
      } catch (error: any) {
        console.log("layDanhSachTaiKhoanDaiLyPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy danh sách vai trò chức năng phân trang
  const layDanhSachVaiTroChucNangPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangVaiTroChucNangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_VAI_TRO_CHUC_NANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachVaiTroChucNang(data);
        setTongSoDongVaiTro(response.data.tong_so_dong);
        console.log("số dòng vai trò phân trang ", tongSoDongVaiTro);
        return data;
        // setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachVaiTroChucNangPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy danh sách nhóm chức năng phân trang
  const layDanhSachNhomChucNangPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangNhomChucNangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_NHOM_CHUC_NANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachNhomChucNang(data);
        setTongSoDongNhom(response.data.tong_so_dong);
        // console.log("data nhóm", data);
        return data;
        // setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachVaiTroChucNangPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 tài khoản người dùng
  const layChiTietTaiKhoanDaiLy = useCallback(
    async (item: ReactQuery.IChiTietTaiKhoanDaiLyParams): Promise<CommonExecute.Execute.IChiTietNguoiSuDung | null> => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.CHI_TIET_TAI_KHOAN_DAI_LY,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const response = responseData.data;
        setChiTietTaiKhoanDaiLy(response.nsd as CommonExecute.Execute.IChiTietNguoiSuDung);
        setMenuNguoiSuDung((response.nsd_menu ?? []) as Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_menu"]>[0]>);
        setChucNangNguoiSuDung((response.nsd_quyen ?? []) as Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_quyen"]>[0]>);
        setDonViQuanLyNguoiSuDung((response.nsd_qly ?? []) as Array<NonNullable<CommonExecute.Execute.IChiTietNguoiSuDung["nsd_qly"]>[0]>);
        // console.log("chi tiết người sử dụng", response);

        return responseData.data.nsd as CommonExecute.Execute.IChiTietNguoiSuDung;
      } catch (error: any) {
        console.log("layChiTietTaiKhoanDaiLy error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  // //Cập nhật hoặc tạo mới 1 phòng ban
  const onUpdateTaiKhoanDaiLy = useCallback(
    async (body: ReactQuery.IUpdateTaiKhoanDaiLyParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_TAI_KHOAN_DAI_LY,
        };
        console.log("params cập nhật", params);

        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          console.log("responseData cập nhật", responseData);
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("Cập nhật tài khoản người dùng error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const getListChiNhanhTheoDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_CHI_NHANH,
      });
      setListChiNhanh((response.data as CommonExecute.Execute.IChiNhanh[]).map(item => ({...item, ten: item.ten_tat})));
      console.log("listChiNhanh response", response.data);
      return response?.data || {data: []};
    } catch (error) {
      console.log("getListPhongBan error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);
  // const getListChiNhanhTheoDoiTac = useCallback(async () => {
  //   try {
  //     const response = await getListChiNhanh();
  //     const updatedData = Array.isArray(response.data) ? [...response.data] : []; // Kiểm tra và xử lý đúng cấu trúc
  //     setListChiNhanh(updatedData.map(item => ({...item, ten_tat: item.ten_tat}))); // Cập nhật state nếu cần
  //     return {data: updatedData};
  //   } catch (error) {
  //     console.log("error", error);
  //     return {data: []}; // Trả về giá trị mặc định khi lỗi
  //   }
  // }, [getListChiNhanh]);
  const layChucNangTheoVaiTro = useCallback(
    async (body: ReactQuery.IDanhSachChucNangTheoVaiTroParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LIET_KE_VAI_TRO_CHUC_NANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data;
        return data;
      } catch (error: any) {
        console.log("layChucNangTheoVaiTro error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const layChucNangTheoNhom = useCallback(
    async (body: ReactQuery.IDanhSachChucNangTheoNhomParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LIET_KE_NHOM_CHUC_NANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data;
        return data;
        // console.log("danh sách chức năng theo nhóm", response);

        // setDanhSachChucNangTheoVaiTro(data);
        // setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layChucNangTheoNhom error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<TaiKhoanDaiLyContextProps>(
    () => ({
      danhSachTaiKhoanDaiLy: danhSachTaiKhoanDaiLy,
      loading: mutateUseCommonExecute.isLoading,
      loadingVT: mutateUseCommonExecute.isLoading,
      tongSoDong,
      tongSoDongVaiTro,
      tongSoDongNhom,
      defaultFormValue,
      listDoiTac,
      listChiNhanh,
      menuNguoiSuDung: menuNguoiSuDung,
      chucNangNguoiSuDung: chucNangNguoiSuDung,
      donViQuanLyNguoiSuDung: donViQuanLyNguoiSuDung,
      danhSachVaiTroChucNang,
      danhSachNhomChucNang,
      chucNangSelected,
      donViSelected,
      menuSelected,
      getListDaiLy,
      listDaiLy,
      setMenuNguoiSuDung,
      setChucNangNguoiSuDung,
      setDonViQuanLyNguoiSuDung,
      setDonViQuanLySelected,
      setMenuSelected,
      setChucNangSelected,
      layChucNangTheoVaiTro,
      layChucNangTheoNhom,
      setListChiNhanh,
      getListChiNhanhTheoDoiTac,
      getListDoiTac,
      onUpdateTaiKhoanDaiLy,
      layDanhSachTaiKhoanDaiLyPhanTrang,
      layChiTietTaiKhoanDaiLy,
      layDanhSachVaiTroChucNangPhanTrang,
      layDanhSachNhomChucNangPhanTrang,
    }),
    [
      danhSachTaiKhoanDaiLy,
      mutateUseCommonExecute,
      listDoiTac,
      tongSoDong,
      tongSoDongVaiTro,
      tongSoDongNhom,
      defaultFormValue,
      listChiNhanh,
      menuNguoiSuDung,
      chucNangNguoiSuDung,
      donViQuanLyNguoiSuDung,
      danhSachVaiTroChucNang,
      danhSachNhomChucNang,
      chucNangSelected,
      donViSelected,
      menuSelected,
      getListDaiLy,
      listDaiLy,
      setMenuNguoiSuDung,
      setChucNangNguoiSuDung,
      setDonViQuanLyNguoiSuDung,
      setDonViQuanLySelected,
      setMenuSelected,
      setChucNangSelected,
      layChucNangTheoVaiTro,
      layChucNangTheoNhom,
      setListChiNhanh,
      getListDoiTac,
      getListChiNhanhTheoDoiTac,
      onUpdateTaiKhoanDaiLy,
      layDanhSachTaiKhoanDaiLyPhanTrang,
      layChiTietTaiKhoanDaiLy,
      layDanhSachVaiTroChucNangPhanTrang,
      layDanhSachNhomChucNangPhanTrang,
    ],
  );

  return <TaiKhoanDaiLyContext.Provider value={value}>{children}</TaiKhoanDaiLyContext.Provider>;
};

export default TaiKhoanDaiLyProvider;
