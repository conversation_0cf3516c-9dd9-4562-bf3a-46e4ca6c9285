import {createContext, useContext} from "react";

import {IHopDongConNguoiContextProps} from "./index.model";
import {ReactQuery} from "@src/@types";

export const HopDongConNguoiContext = createContext<IHopDongConNguoiContextProps>({
  listHopDong: [],
  listDoiTac: [],
  listChiNhanh: [],
  listSanPham: [],
  listChuongTrinhBaoHiem: [],
  listPhuongThucKhaiThac: [],
  listDaiLyKhaiThac: [],
  listDonViBoiThuongTPA: [],
  listCanBo: [],
  listPhongBan: [],
  listNguyenTe: [],
  chiTietHopDong: null,

  tongSoDongHopDong: 0,
  loading: false,
  filterHopDongParams: {},
  windowHeight: 0,
  listDongBaoHiemHopDongConNguoi: [],
  listDonViDongTai: [],
  listTaiBaoHiemHopDongConNguoi: [],
  danhSachLichSuBoiThuong: [],
  chiTietLichSuBoiThuong: undefined,

  timKiemPhanTrangNguoiDuocBaoHiemParams: {},
  listNguoiDuocBaoHiem: [],
  tongSoDongNguoiDuocBaoHiem: 0,
  listGoiBaoHiem: [],
  chiTietNguoiDuocBaoHiem: {so_id: "", so_id_dt: ""},
  loadingQuyenLoi: false,
  listThongTinThanhToanHopDongConNguoi: [],
  listQuyenLoiNguoiDuocBaoHiem: [],

  timKiemPhanTrangHopDongBaoHiem: () => Promise.resolve(),
  // getListChiNhanhTheoDoiTac: () => Promise.resolve(),
  getListDoiTac: () => Promise.resolve(),
  getListChiNhanh: params => Promise.resolve(),
  getListPhongBan: params => Promise.resolve(),
  getListCanBoQuanLy: params =>
    Promise.resolve({
      data: [],
      tong_so_dong: 0,
    }),
  getListSanPham: params => Promise.resolve(),
  getListChuongTrinHBaoHiem: params => Promise.resolve(),
  getListPhuongThucKhaiThac: params => Promise.resolve(),
  getListDaiLyKhaiThac: params => Promise.resolve(),
  getListDonViBoiThuong: params => Promise.resolve(),
  searchKhachHang: params => Promise.resolve({tong_so_dong: 0, data: []}),
  searchDaiLy: params => Promise.resolve({tong_so_dong: 0, data: []}),
  getChiTietHopDong: (params: ReactQuery.IChiTietHopDongConNguoiParams) => Promise.resolve({} as CommonExecute.Execute.IHopDongConNguoi),
  updateHopDong: (params: ReactQuery.IUpdateHopDongParams) =>
    Promise.resolve({
      data: 0,
      output: {
        so_hd: "",
        so_id: 0,
      },
    }),
  setFilterHopDongParams: () => {},

  timKiemPhanTrangNguoiDuocBaoHiem: () => Promise.resolve(),
  getChiTietNguoiDuocBaoHiem: (params: ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi) => Promise.resolve(),
  luuThongTinNguoiDuocBaoHiem: (params: ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi) => Promise.resolve(),
  setTimKiemPhanTrangNguoiDuocBaoHiemParams: () => {},
  timKiemPhanTrangGoiBaoHiem: (params: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams) => Promise.resolve(),
  setChiTietNguoiDuocBaoHiem: () => {},
  lietKeDieuKhoanNguoiDuocBaoHiem: (params: ReactQuery.ILietKeDieuKhoanNguoiDuocBaoHiemParams) => Promise.resolve(),
  luuDieuKhoanNguoiDuocBaoHiem: (params: ReactQuery.ILuuDieuKhoanNguoiDuocBaoHiemParams) => Promise.resolve(),
  lietKeDieuKhoanBoSungNguoiDuocBaoHiem: (params: ReactQuery.ILietKeDieuKhoanBoSungNguoiDuocBaoHiemParams) => Promise.resolve(),
  luuDieuKhoanBoSungNguoiDuocBaoHiem: (params: ReactQuery.ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams) => Promise.resolve(),
  getListNguoiPhuThuoc: (params: ReactQuery.ILietKeDanhSachNguoiPhuThuocHopDongConNguoiParams) => Promise.resolve(),
  getChiTietNguoiPhuThuoc: (params: ReactQuery.IChiTietNguoiPhuThuocHopDongConNguoiParams) => Promise.resolve(),
  capNhatNguoiPhuThuoc: (params: ReactQuery.ICapNhatNguoiPhuThuocHopDongConNguoiParams) => Promise.resolve(),
  getDanhSachFileThumbnailTheoDoiTuong: (params: ReactQuery.IGetFileThumbnailParams) => Promise.resolve(),
  uploadFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise.resolve(),
  deleteFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise.resolve(),
  layThongTinThanhToanCuaHopDongBaoHiemConNguoi: (params: ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams) => Promise.resolve(),
  updateKyThanhToan: (params: ReactQuery.IUpdateKyThanhToanParams) => Promise.resolve(),
  layChiTietKyThanhToan: (params: ReactQuery.IChiTietKyThanhToanParams) => Promise.resolve(),
  layChiTietThongTinCauHinhDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise.resolve(),
  xoaThongTinDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise.resolve(),
  lietKeDanhSachCacDoiTuongDaDuocApDungDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise.resolve(),
  updateCauHinhDongBaoHiem: (params: ReactQuery.IUpdateCauHinhDongBHParams) => Promise.resolve(),
  layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise.resolve(),
  layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise.resolve(),
  layChiTietThongTinCauHinhTaiBH: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise.resolve(),
  updateCauHinhTaiBaoHiem: (params: ReactQuery.IUpdateCauHinhTaiBHParams) => Promise.resolve(false),
  xoaThongTinTaiBH: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise.resolve(),
  updateDoiTuongApDungTaiBH: (params: ReactQuery.IUpdateDoiTuongApDungTaiBHParams) => Promise.resolve(false),
  lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise.resolve(),
  exportPdfHopDong: (params: ReactQuery.IExportPDFHopDongParams) => Promise.resolve(),
  updateDoiTuongApDungTyLeDongBH: () => Promise.resolve(false),
  layDanhSachCauHoiDanhGiaSucKhoe: (_params: ReactQuery.ILietKeCauHoiDanhGiaSucKhoeConNguoiParams) => Promise.resolve({cau_hoi: [], gia_tri: []}),
  luuDanhGiaSucKhoe: (params: ReactQuery.ILuuDanhGiaSucKhoeConNguoiParams) => Promise.resolve(false),
  layDanhSachLichSuBoiThuongHopDong: (params: ReactQuery.ILayDanhSachLichSuBoiThuongHopDongParams) => Promise.resolve([]),
  layChiTietLichSuBoiThuongHopDong: (params: ReactQuery.ILayChiTietLichSuBoiThuongHopDongParams) => Promise.resolve({}),
  XoaLichSuBoiThuong: (params: ReactQuery.ILayChiTietLichSuBoiThuongHopDongParams) => Promise.resolve(false),
  capNhatLichSuBoiThuong: (params: ReactQuery.ICapNhatLichSuBoiThuongHopDongParams) => Promise.resolve(false),
  // Bệnh viện API functions cho Hợp đồng con người
  timKiemBenhVienPhanTrangHopDongConNguoi: (params: ReactQuery.ITimKiemBenhVienHopDongConNguoiParams) => Promise.resolve({}),
  layDanhSachBenhVienDaLuuHopDongConNguoi: (params: ReactQuery.ILayDanhSachBenhVienDaLuuHopDongConNguoiParams) => Promise.resolve({}),
  luuCauHinhBenhVienHopDongConNguoi: (params: ReactQuery.ILuuCauHinhBenhVienHopDongConNguoiParams) => Promise.resolve(false),

  // Mã bệnh API functions cho Hợp đồng con người
  timKiemMaBenhPhanTrangHopDongConNguoi: (params: ReactQuery.ITimKiemMaBenhHopDongConNguoiParams) => Promise.resolve({}),
  layDanhSachMaBenhDaLuuHopDongConNguoi: (params: ReactQuery.ILayDanhSachMaBenhDaLuuHopDongConNguoiParams) => Promise.resolve({}),
  luuCauHinhMaBenhHopDongConNguoi: (params: ReactQuery.ILuuCauHinhMaBenhHopDongConNguoiParams) => Promise.resolve(false),
});

export const useHopDongConNguoiContext = () => useContext(HopDongConNguoiContext);
